"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartGateway = void 0;
const common_1 = require("@nestjs/common");
const socket_io_1 = require("socket.io");
const websockets_1 = require("@nestjs/websockets");
const redis_service_1 = require("../utils/redis/redis.service");
let CartGateway = class CartGateway {
    constructor(redisService) {
        this.redisService = redisService;
        this.logger = new common_1.Logger('CartGateway');
        this.clientInfoMap = new Map();
        this.cartStates = new Map();
        try {
            this.logger.log('Initializing Cart Gateway');
            // Get Redis clients from centralized service
            this.redisSubClient = this.redisService.getSubClient();
            this.redisPubClient = this.redisService.getPubClient();
            this.redisSubClient.on('message', (channel, message) => {
                this.handleRedisMessage(channel, message);
            });
            this.redisSubClient.subscribe('cart-updates');
        }
        catch (error) {
            this.logger.error('Failed to initialize Redis clients', error);
            throw error;
        }
    }
    async handleConnection(client) {
        try {
            this.logger.log(`Client connected to cart socket: ${client.id}`);
        }
        catch (error) {
            this.logger.error('Cart connection handler failed', error);
        }
    }
    async handleDisconnect(client) {
        try {
            const cartInfo = this.clientInfoMap.get(client.id);
            if (cartInfo) {
                const roomId = `cart:${cartInfo.appointmentId}`;
                client.leave(roomId);
                this.clientInfoMap.delete(client.id);
                this.logger.log(`Client disconnected from cart: ${client.id}`);
            }
        }
        catch (error) {
            this.logger.error('Error handling cart disconnect', error);
        }
    }
    async handleJoinCart(client, { appointmentId }) {
        try {
            const roomId = `cart:${appointmentId}`;
            client.join(roomId);
            this.clientInfoMap.set(client.id, { appointmentId });
            // Send current cart state to the joining client
            const currentState = this.cartStates.get(appointmentId) || {
                isAdjustmentInInvoice: false,
                discountObject: { label: '0%', value: '0' },
                invoiceAmount: 0
            };
            client.emit('cartStateSync', currentState);
            this.logger.log(`Client ${client.id} joined cart ${appointmentId}`);
            client.to(roomId).emit('userJoined', {
                clientId: client.id,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            this.logger.error('Error joining cart', error);
            throw new Error('Failed to join cart');
        }
    }
    async handleCartChange(client, payload) {
        try {
            const { appointmentId, action, value, state } = payload;
            const roomId = `cart:${appointmentId}`;
            this.logger.log(`Received cartChange event from client ${client.id}`, {
                action,
                appointmentId,
                value,
                state
            });
            if (action === 'updateState' && state) {
                // Update the stored cart state
                const currentState = this.cartStates.get(appointmentId) || {
                    isAdjustmentInInvoice: false,
                    discountObject: { label: '0%', value: '0' },
                    invoiceAmount: 0
                };
                this.cartStates.set(appointmentId, {
                    ...currentState,
                    ...state
                });
                // Broadcast state update to all clients in the room except sender
                client.to(roomId).emit('cartStateUpdated', state);
            }
            else if (action === 'checkout') {
                client.to(roomId).emit('cartCheckedOut', {
                    appointmentId,
                    timestamp: new Date().toISOString()
                });
            }
            else {
                // Regular cart updates
                client.to(roomId).emit('cartUpdated', {
                    action,
                    appointmentId,
                    value,
                    timestamp: new Date().toISOString()
                });
            }
            // Publish to Redis for other server instances
            await this.publishCartUpdate(appointmentId, {
                action,
                appointmentId,
                value,
                state,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            this.logger.error('Error handling cart change', error);
            throw new Error('Failed to process cart change');
        }
    }
    async handleRedisMessage(channel, message) {
        if (channel === 'cart-updates') {
            const { event, appointmentId, data } = JSON.parse(message);
            const roomId = `cart:${appointmentId}`;
            this.server.to(roomId).emit(event, data);
            this.logger.log(`Handling Redis message on channel ${channel}`, {
                event,
                appointmentId,
                data
            });
        }
    }
    async publishCartUpdate(appointmentId, data) {
        this.logger.log(`Publishing cart update to Redis for appointmentId ${appointmentId}`, {
            data
        });
        await this.redisPubClient.publish('cart-updates', JSON.stringify({
            event: 'cartUpdated',
            appointmentId,
            data
        }));
    }
};
exports.CartGateway = CartGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], CartGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('joinCart'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], CartGateway.prototype, "handleJoinCart", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('cartChange'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], CartGateway.prototype, "handleCartChange", null);
exports.CartGateway = CartGateway = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: { origin: '*' },
        namespace: 'events/cart/',
        pingInterval: 25000,
        pingTimeout: 60000
    }),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], CartGateway);
//# sourceMappingURL=socket.cart.gateway.js.map