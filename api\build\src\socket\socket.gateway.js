"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatGateway = void 0;
//TODO :
// 1. See if we can move the join room logic to handleConnection
// 2. Add auth to sockets
// 3. Add redis functionality
const common_1 = require("@nestjs/common");
const socket_io_1 = require("socket.io");
const websockets_1 = require("@nestjs/websockets");
const create_chat_message_dto_1 = require("../chat-room/dto/create-chat-message.dto");
const chat_user_sessions_service_1 = require("./chat-user-sessions.service");
const redis_service_1 = require("../utils/redis/redis.service");
// @UseFilters(WsExceptionFilter)
let ChatGateway = class ChatGateway {
    constructor(connectedUserService, redisService) {
        this.connectedUserService = connectedUserService;
        this.redisService = redisService;
        this.logger = new common_1.Logger('ChatGateway');
        try {
            console.log('Constructor called');
            // Get Redis clients from centralized service
            this.redisSubClient = this.redisService.getSubClient();
            this.redisPubClient = this.redisService.getPubClient();
            this.redisSubClient.on('message', this.handleRedisMessage.bind(this));
            this.redisSubClient.subscribe('chat-message');
            this.currentUser = {
                id: '219c062a-f3aa-4aff-9e16-ff54f6ff14c5',
                name: 'Virat'
            };
        }
        catch (error) {
            this.logger.error('Error initializing Redis clients', error);
            throw error;
        }
    }
    async onModuleInit() {
        console.log('ChatGateway initialized');
        this.logger.log('ChatGateway initialized');
        await this.connectedUserService.deleteAll();
    }
    async handleConnection(socket) {
        try {
            console.log('Handle Connection');
            // const user = this.authenticateSocket(socket);
            await this.initializeUserConnection({ name: this.currentUser.name, id: this.currentUser.id }, socket);
        }
        catch (error) {
            console.log('error in handle connection');
            this.handleConnectionError(socket, error);
        }
    }
    async handleDisconnect(socket) {
        await this.connectedUserService.delete(socket.id);
        await this.redisPubClient.hdel(`chatRoom:${socket.data.roomId}`, socket.id);
        this.logger.log(`Client disconnected: ${socket.id}`);
    }
    async handleJoinRoom(client, { chatRoomId, userId }) {
        console.log('Chat Room Joining Details', { chatRoomId, userId });
        console.log({ client });
        await this.connectedUserService.create(userId, client.id);
        client.join(chatRoomId);
        client.emit('joinedRoom', chatRoomId);
        this.logger.log(`Client ${client.id} joined room ${chatRoomId}`);
    }
    async onSendMessage(createMessageDto, client) {
        const userId = createMessageDto.senderId;
        const { chatRoomId, otherUserId } = createMessageDto;
        try {
            // Log client ID for debugging
            this.logger.log(`Client ${client.id} sending message`);
            //Should we add the logic to add the message to  db here?
            this.logger.log(`User ID ${userId} sent a new message in Room ID ${chatRoomId}`);
            console.log({ chatRoomId, otherUserId });
            const otherUserDetails = await this.connectedUserService.get(otherUserId);
            console.log({ otherUserDetails });
            //Publishing event to redis
            await this.redisPubClient.publish('chat-message', JSON.stringify({
                event: 'messageSent',
                roomId: otherUserDetails.socketId,
                message: createMessageDto
            }));
            //emit message to socket connection
            await this.emitToSocket(otherUserDetails.socketId, 'messageSent', createMessageDto);
            await this.emitToSocket(otherUserDetails.socketId, 'chat-room-notification', {
                some: 'Send something'
            });
        }
        catch (error) {
            console.log('🚀 ~ ChatGateway ~ error:', error);
            this.logger.error(`Failed to send message in Room ID ${chatRoomId} by User ID ${userId}: ${error.message}`, error.stack);
            throw new websockets_1.WsException('Error occurred while sending the message.');
        }
    }
    async emitToSocket(socketId, event, payload) {
        return new Promise((resolve, reject) => {
            this.server.emit(event, payload, (response) => {
                if (response && response.error) {
                    reject(new Error(response.error));
                }
                else {
                    resolve();
                }
            });
        });
    }
    async initializeUserConnection(userPayload, socket) {
        socket.data.user = userPayload;
        // console.log("socket-->",);
        // await this.connectedUserService.create(userPayload.id, socket.id);
        // const rooms = await this.roomService.findByUserId(userPayload.id);
        // this.server.to(socket.id).emit('userAllRooms', rooms);
        this.logger.log(`Client connected: ${socket.id} - User ID: ${userPayload.id}`);
    }
    handleConnectionError(socket, error) {
        this.logger.error(`Connection error for socket ${socket.id}: ${error.message}`);
        socket.emit('exception', 'Authentication error');
        socket.disconnect();
    }
    async handleRedisMessage(channel, message) {
        if (channel === 'chat-message') {
            const { event, roomId, message: newMessage } = JSON.parse(message);
            this.server.to(roomId).emit(event, newMessage);
        }
    }
};
exports.ChatGateway = ChatGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], ChatGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('joinRoom'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], ChatGateway.prototype, "handleJoinRoom", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('sendMessage'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_chat_message_dto_1.CreateChatMessageDto,
        socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], ChatGateway.prototype, "onSendMessage", null);
exports.ChatGateway = ChatGateway = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: { origin: '*' },
        namespace: '/events/'
        // path: '/socket.io'
    }),
    __metadata("design:paramtypes", [chat_user_sessions_service_1.ChatUserSessionsService,
        redis_service_1.RedisService])
], ChatGateway);
//# sourceMappingURL=socket.gateway.js.map