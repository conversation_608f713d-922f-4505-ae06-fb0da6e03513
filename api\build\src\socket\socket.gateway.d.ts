import { Socket, Server } from 'socket.io';
import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { CreateChatMessageDto } from '../chat-room/dto/create-chat-message.dto';
import { ChatUserSessionsService } from './chat-user-sessions.service';
import { RedisService } from '../utils/redis/redis.service';
import { Redis, Cluster } from 'ioredis';
export declare class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly connectedUserService;
    private readonly redisService;
    server: Server;
    private readonly logger;
    protected redisSubClient: Redis | Cluster;
    protected redisPubClient: Redis | Cluster;
    private currentUser;
    constructor(connectedUserService: ChatUserSessionsService, redisService: RedisService);
    onModuleInit(): Promise<void>;
    handleConnection(socket: Socket): Promise<void>;
    handleDisconnect(socket: Socket): Promise<void>;
    handleJoinRoom(client: Socket, { chatRoomId, userId }: {
        userId: string;
        chatRoomId: string;
    }): Promise<void>;
    onSendMessage(createMessageDto: CreateChatMessageDto, client: Socket): Promise<void>;
    emitToSocket(socketId: string, event: string, payload: any): Promise<void>;
    private initializeUserConnection;
    handleConnectionError(socket: Socket, error: Error): void;
    handleRedisMessage(channel: string, message: string): Promise<void>;
}
