"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const analytics_controller_1 = require("./analytics.controller");
const analytics_service_1 = require("./analytics.service");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const role_module_1 = require("../roles/role.module");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const analytics_document_request_entity_1 = require("./entities/analytics-document-request.entity");
const send_document_service_1 = require("../utils/common/send-document.service");
const sqs_module_1 = require("../utils/aws/sqs/sqs.module");
const s3_module_1 = require("../utils/aws/s3/s3.module");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
const tab_activity_module_1 = require("../tab-activity/tab-activity.module");
const patient_vaccinations_entity_1 = require("../patient-vaccinations/entities/patient-vaccinations.entity");
const emr_entity_1 = require("../emr/entities/emr.entity");
const lab_report_entity_1 = require("../clinic-lab-report/entities/lab-report.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const appointment_doctor_entity_1 = require("../appointments/entities/appointment-doctor.entity");
const merged_invoice_document_entity_1 = require("../invoice/entities/merged-invoice-document.entity");
const merged_payment_receipt_document_entity_1 = require("../payment-details/entities/merged-payment-receipt-document.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const config_1 = require("@nestjs/config");
let AnalyticsModule = class AnalyticsModule {
};
exports.AnalyticsModule = AnalyticsModule;
exports.AnalyticsModule = AnalyticsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                invoice_entity_1.InvoiceEntity,
                payment_details_entity_1.PaymentDetailsEntity,
                patient_entity_1.Patient,
                owner_brand_entity_1.OwnerBrand,
                appointment_entity_1.AppointmentEntity,
                clinic_entity_1.ClinicEntity,
                analytics_document_request_entity_1.AnalyticsDocumentRequestEntity,
                patient_vaccinations_entity_1.PatientVaccination,
                emr_entity_1.Emr,
                lab_report_entity_1.LabReport,
                appointment_details_entity_1.AppointmentDetailsEntity,
                appointment_doctor_entity_1.AppointmentDoctorsEntity,
                merged_invoice_document_entity_1.MergedInvoiceDocumentEntity,
                merged_payment_receipt_document_entity_1.MergedPaymentReceiptDocumentEntity
            ]),
            config_1.ConfigModule,
            role_module_1.RoleModule,
            sqs_module_1.SqsModule,
            s3_module_1.S3Module,
            ses_module_1.SESModule,
            whatsapp_module_1.WhatsappModule,
            tab_activity_module_1.TabActivityModule
        ],
        controllers: [analytics_controller_1.AnalyticsController],
        providers: [analytics_service_1.AnalyticsService, send_document_service_1.SendDocuments, winston_logger_service_1.WinstonLogger],
        exports: [analytics_service_1.AnalyticsService]
    })
], AnalyticsModule);
//# sourceMappingURL=analytics.module.js.map