"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsDocumentStatusResponseDto = exports.ShareAnalyticsDocumentsResponseDto = exports.ShareAnalyticsDocumentsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const analytics_document_request_entity_1 = require("../entities/analytics-document-request.entity");
class ShareAnalyticsDocumentsDto {
}
exports.ShareAnalyticsDocumentsDto = ShareAnalyticsDocumentsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Clinic ID',
        example: 'uuid'
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "clinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Brand ID',
        example: 'uuid'
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "brandId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Owner ID',
        example: 'uuid'
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "ownerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document types to include',
        example: ['invoices', 'receipts'],
        enum: analytics_document_request_entity_1.AnalyticsDocumentType,
        isArray: true
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(analytics_document_request_entity_1.AnalyticsDocumentType, { each: true }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Array)
], ShareAnalyticsDocumentsDto.prototype, "documentTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date for the analytics period (maximum 31 days range)',
        example: '2024-01-01'
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date for the analytics period (maximum 31 days from start date, cannot be in future)',
        example: '2024-01-31'
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient email address',
        example: '<EMAIL>'
    }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "recipientEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient type',
        example: 'client',
        enum: analytics_document_request_entity_1.RecipientType
    }),
    (0, class_validator_1.IsEnum)(analytics_document_request_entity_1.RecipientType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "recipientType", void 0);
class ShareAnalyticsDocumentsResponseDto {
}
exports.ShareAnalyticsDocumentsResponseDto = ShareAnalyticsDocumentsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique request ID for tracking',
        example: 'uuid'
    }),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsResponseDto.prototype, "requestId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current status of the request',
        example: 'processing'
    }),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsResponseDto.prototype, "status", void 0);
class AnalyticsDocumentStatusResponseDto {
}
exports.AnalyticsDocumentStatusResponseDto = AnalyticsDocumentStatusResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Request ID',
        example: 'uuid'
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusResponseDto.prototype, "requestId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current status',
        example: 'completed'
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document types requested',
        example: ['invoices', 'receipts']
    }),
    __metadata("design:type", Array)
], AnalyticsDocumentStatusResponseDto.prototype, "documentTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date of the period',
        example: '2024-01-01T00:00:00.000Z'
    }),
    __metadata("design:type", Date)
], AnalyticsDocumentStatusResponseDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date of the period',
        example: '2024-01-31T23:59:59.999Z'
    }),
    __metadata("design:type", Date)
], AnalyticsDocumentStatusResponseDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient email',
        example: '<EMAIL>'
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusResponseDto.prototype, "recipientEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Request creation timestamp',
        example: '2024-01-15T10:30:00.000Z'
    }),
    __metadata("design:type", Date)
], AnalyticsDocumentStatusResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Processing completion timestamp',
        example: '2024-01-15T10:35:00.000Z',
        required: false
    }),
    __metadata("design:type", Date)
], AnalyticsDocumentStatusResponseDto.prototype, "processedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File expiration timestamp',
        example: '2024-01-16T10:30:00.000Z'
    }),
    __metadata("design:type", Date)
], AnalyticsDocumentStatusResponseDto.prototype, "expiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'PDF file key (if completed)',
        example: 'analytics_documents/uuid/file.pdf',
        required: false
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusResponseDto.prototype, "pdfFileKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Excel file key (if completed)',
        example: 'analytics_documents/uuid/report.xlsx',
        required: false
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusResponseDto.prototype, "excelFileKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Error message (if failed)',
        example: 'No documents found for the specified period',
        required: false
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusResponseDto.prototype, "errorMessage", void 0);
//# sourceMappingURL=share-analytics-documents.dto.js.map