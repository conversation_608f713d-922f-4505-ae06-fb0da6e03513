"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.queues = void 0;
exports.getEnvSpecificQueues = getEnvSpecificQueues;
const process_create_emr_handler_1 = require("./handlers/process_create_emr.handler");
const process_send_documents_handler_1 = require("./handlers/process_send_documents.handler");
const process_invoice_tasks_handler_1 = require("./handlers/process_invoice_tasks.handler");
const process_availability_update_handler_1 = require("./handlers/process_availability_update.handler");
const process_availability_maintenance_handler_1 = require("./handlers/process_availability_maintenance.handler");
exports.queues = {
    NidanaCreateEMR: {
        name: 'NidanaCreateEMR',
        delaySeconds: 0,
        handler: process_create_emr_handler_1.ProcessEMRHandler,
        maxReceiveCount: 5,
        messageRetentionPeriod: 86400,
        dlqName: 'NidanaDeadLetterQueue'
    },
    NidanaSendDocuments: {
        name: 'NidanaSendDocuments',
        delaySeconds: 0,
        handler: process_send_documents_handler_1.ProcessSendDocumentsHandler,
        maxReceiveCount: 5,
        messageRetentionPeriod: 86400,
        dlqName: 'NidanaDeadLetterQueue'
    },
    NidanaInvoiceTasks: {
        name: 'NidanaInvoiceTasks',
        delaySeconds: 0,
        handler: process_invoice_tasks_handler_1.ProcessInvoiceTasksHandler,
        maxReceiveCount: 5,
        messageRetentionPeriod: 86400,
        dlqName: 'NidanaDeadLetterQueue'
    },
    NidanaAvailabilityUpdate: {
        name: 'NidanaAvailabilityUpdate',
        delaySeconds: 0,
        handler: process_availability_update_handler_1.ProcessAvailabilityUpdateHandler,
        maxReceiveCount: 5,
        messageRetentionPeriod: 86400,
        dlqName: 'NidanaDeadLetterQueue'
    },
    NidanaAvailabilityMaintenance: {
        name: 'NidanaAvailabilityMaintenance',
        delaySeconds: 0,
        handler: process_availability_maintenance_handler_1.ProcessAvailabilityMaintenanceHandler,
        maxReceiveCount: 3, // Fewer retries for maintenance tasks
        messageRetentionPeriod: 86400,
        dlqName: 'NidanaDeadLetterQueue'
    }
};
function getEnvSpecificQueues(env) {
    // Use 'Uat' prefix when environment is 'development', otherwise capitalize first letter
    const prefix = env === 'development'
        ? 'Uat'
        : env.charAt(0).toUpperCase() + env.slice(1);
    const envSpecificQueues = {};
    Object.entries(exports.queues).forEach(([key, value]) => {
        envSpecificQueues[key] = {
            name: `${prefix}${value.name}`,
            delaySeconds: value.delaySeconds,
            handler: value.handler,
            maxReceiveCount: value.maxReceiveCount,
            messageRetentionPeriod: value.messageRetentionPeriod,
            dlqName: `${prefix}${value.dlqName}`
        };
    });
    return envSpecificQueues;
}
//# sourceMappingURL=sqs-queue.config.js.map