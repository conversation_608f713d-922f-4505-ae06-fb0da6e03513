import { Response } from 'express';
import { Role } from '../roles/role.enum';
import { AnalyticsService } from './analytics.service';
import { SendDocuments } from '../utils/common/send-document.service';
import { Request } from 'express';
interface RequestWithUser extends Request {
    user: {
        id: string;
        role: Role;
        clinicId?: string;
        brandId?: string;
    };
}
import { DownloadAnalyticsReportDto, GetRevenueChartDataDto, RevenueChartDataPoint, CollectedPaymentsChartDataPoint, GetAppointmentsChartDataDto, AppointmentsChartResponse, DoctorSummaryResponseDto, GetDoctorSummaryDto, SummaryResponseDto, GetSummaryDto } from './dto/analytics.dto';
import { ShareAnalyticsDocumentsDto, ShareAnalyticsDocumentsResponseDto, AnalyticsDocumentStatusResponseDto } from './dto/share-analytics-documents.dto';
export declare class AnalyticsController {
    private readonly analyticsService;
    private readonly sendDocuments;
    constructor(analyticsService: AnalyticsService, sendDocuments: SendDocuments);
    getRevenueChartData(dto: GetRevenueChartDataDto): Promise<RevenueChartDataPoint[]>;
    getCollectedPaymentsChartData(dto: GetRevenueChartDataDto): Promise<CollectedPaymentsChartDataPoint[]>;
    getAppointmentsChartData(dto: GetAppointmentsChartDataDto): Promise<AppointmentsChartResponse>;
    downloadReport(dto: DownloadAnalyticsReportDto, res: Response): Promise<void>;
    getDoctorSummary(dto: GetDoctorSummaryDto): Promise<DoctorSummaryResponseDto[]>;
    getSummary(dto: GetSummaryDto): Promise<SummaryResponseDto>;
    shareAnalyticsDocuments(shareRequest: ShareAnalyticsDocumentsDto, req: RequestWithUser): Promise<ShareAnalyticsDocumentsResponseDto>;
    getDocumentShareStatus(requestId: string, req: RequestWithUser): Promise<AnalyticsDocumentStatusResponseDto>;
}
export {};
