import { AnalyticsDocumentType, RecipientType } from '../entities/analytics-document-request.entity';
export declare class ShareAnalyticsDocumentsDto {
    clinicId: string;
    brandId: string;
    ownerId: string;
    documentTypes: AnalyticsDocumentType[];
    startDate: string;
    endDate: string;
    recipientEmail: string;
    recipientType: RecipientType;
}
export declare class ShareAnalyticsDocumentsResponseDto {
    requestId: string;
    status: string;
}
export declare class AnalyticsDocumentStatusResponseDto {
    requestId: string;
    status: string;
    documentTypes: AnalyticsDocumentType[];
    startDate: Date;
    endDate: Date;
    recipientEmail: string;
    createdAt: Date;
    processedAt?: Date;
    expiresAt: Date;
    pdfFileKey?: string;
    excelFileKey?: string;
    errorMessage?: string;
}
