"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileCleanupService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const winston_logger_service_1 = require("../logger/winston-logger.service");
const s3_service_1 = require("../aws/s3/s3.service");
const analytics_document_request_entity_1 = require("../../analytics/entities/analytics-document-request.entity");
let FileCleanupService = class FileCleanupService {
    constructor(logger, s3Service, analyticsDocumentRequestRepository) {
        this.logger = logger;
        this.s3Service = s3Service;
        this.analyticsDocumentRequestRepository = analyticsDocumentRequestRepository;
    }
    async handleExpiredDocuments() {
        this.logger.log('Starting expired documents cleanup job');
        const expiredRequests = await this.analyticsDocumentRequestRepository.find({
            where: {
                expiresAt: new Date(),
            },
        });
        if (expiredRequests.length === 0) {
            this.logger.log('No expired documents to clean up');
            return;
        }
        this.logger.log(`Found ${expiredRequests.length} expired document requests to clean up`);
        for (const request of expiredRequests) {
            try {
                if (request.pdfFileKey) {
                    await this.s3Service.deleteFile(request.pdfFileKey);
                    this.logger.log(`Deleted PDF file from S3: ${request.pdfFileKey}`);
                }
                if (request.excelFileKey) {
                    await this.s3Service.deleteFile(request.excelFileKey);
                    this.logger.log(`Deleted Excel file from S3: ${request.excelFileKey}`);
                }
                await this.analyticsDocumentRequestRepository.delete(request.id);
                this.logger.log(`Deleted document request record from database: ${request.id}`);
            }
            catch (error) {
                this.logger.error(`Failed to clean up document request ${request.id}`, { error });
            }
        }
        this.logger.log('Finished expired documents cleanup job');
    }
};
exports.FileCleanupService = FileCleanupService;
__decorate([
    (0, schedule_1.Cron)('0 0 * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FileCleanupService.prototype, "handleExpiredDocuments", null);
exports.FileCleanupService = FileCleanupService = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, typeorm_1.InjectRepository)(analytics_document_request_entity_1.AnalyticsDocumentRequestEntity)),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        s3_service_1.S3Service,
        typeorm_2.Repository])
], FileCleanupService);
//# sourceMappingURL=cleanup.service.js.map