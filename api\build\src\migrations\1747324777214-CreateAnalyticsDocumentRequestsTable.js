"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAnalyticsDocumentRequestsTable1747324777214 = void 0;
const typeorm_1 = require("typeorm");
class CreateAnalyticsDocumentRequestsTable1747324777214 {
    constructor() {
        this.name = 'CreateAnalyticsDocumentRequestsTable1747324777214';
    }
    async up(queryRunner) {
        // Create enum types for status, document types, and recipient type
        await queryRunner.query(`CREATE TYPE "public"."analytics_document_request_status_enum" AS ENUM('pending', 'processing', 'completed', 'failed', 'expired')`);
        await queryRunner.query(`CREATE TYPE "public"."analytics_document_type_enum" AS ENUM('invoices', 'receipts', 'credit_notes')`);
        await queryRunner.query(`CREATE TYPE "public"."analytics_recipient_type_enum" AS ENUM('client', 'other')`);
        // Create analytics_document_requests table
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'analytics_document_requests',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'uuid'
                },
                {
                    name: 'request_id',
                    type: 'varchar',
                    isUnique: true,
                    isNullable: false
                },
                {
                    name: 'owner_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'brand_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'requested_by_user_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'document_types',
                    type: 'jsonb',
                    isNullable: false,
                    comment: 'Array of document types: invoices, receipts, credit_notes'
                },
                {
                    name: 'start_date',
                    type: 'timestamp with time zone',
                    isNullable: false
                },
                {
                    name: 'end_date',
                    type: 'timestamp with time zone',
                    isNullable: false
                },
                {
                    name: 'recipient_email',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'recipient_type',
                    type: 'enum',
                    enumName: 'analytics_recipient_type_enum',
                    isNullable: false
                },
                {
                    name: 'status',
                    type: 'enum',
                    enumName: 'analytics_document_request_status_enum',
                    isNullable: false,
                    default: "'pending'"
                },
                {
                    name: 'pdf_file_key',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'excel_file_key',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'error_message',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'processed_at',
                    type: 'timestamp with time zone',
                    isNullable: true
                },
                {
                    name: 'expires_at',
                    type: 'timestamp with time zone',
                    isNullable: false,
                    comment: 'Files will be cleaned up after this date'
                },
                {
                    name: 'created_at',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                }
            ]
        }), true);
        // Create indexes for performance
        await queryRunner.createIndex('analytics_document_requests', new typeorm_1.TableIndex({
            name: 'IDX_analytics_document_requests_request_id',
            columnNames: ['request_id']
        }));
        await queryRunner.createIndex('analytics_document_requests', new typeorm_1.TableIndex({
            name: 'IDX_analytics_document_requests_clinic_id',
            columnNames: ['clinic_id']
        }));
        await queryRunner.createIndex('analytics_document_requests', new typeorm_1.TableIndex({
            name: 'IDX_analytics_document_requests_status',
            columnNames: ['status']
        }));
        await queryRunner.createIndex('analytics_document_requests', new typeorm_1.TableIndex({
            name: 'IDX_analytics_document_requests_expires_at',
            columnNames: ['expires_at']
        }));
        await queryRunner.createIndex('analytics_document_requests', new typeorm_1.TableIndex({
            name: 'IDX_analytics_document_requests_created_at',
            columnNames: ['created_at']
        }));
    }
    async down(queryRunner) {
        // Drop indexes first
        await queryRunner.dropIndex('analytics_document_requests', 'IDX_analytics_document_requests_created_at');
        await queryRunner.dropIndex('analytics_document_requests', 'IDX_analytics_document_requests_expires_at');
        await queryRunner.dropIndex('analytics_document_requests', 'IDX_analytics_document_requests_status');
        await queryRunner.dropIndex('analytics_document_requests', 'IDX_analytics_document_requests_clinic_id');
        await queryRunner.dropIndex('analytics_document_requests', 'IDX_analytics_document_requests_request_id');
        // Drop table
        await queryRunner.dropTable('analytics_document_requests');
        // Drop enum types
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."analytics_document_request_status_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."analytics_document_type_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."analytics_recipient_type_enum"`);
    }
}
exports.CreateAnalyticsDocumentRequestsTable1747324777214 = CreateAnalyticsDocumentRequestsTable1747324777214;
//# sourceMappingURL=1747324777214-CreateAnalyticsDocumentRequestsTable.js.map