"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessSendDocumentsHandler = void 0;
const common_1 = require("@nestjs/common");
const send_document_service_1 = require("../../../common/send-document.service");
const emr_service_1 = require("../../../../emr/emr.service");
const winston_logger_service_1 = require("../../../logger/winston-logger.service");
const s3_service_1 = require("../../../aws/s3/s3.service");
const statement_service_1 = require("../../../../statement/statement.service");
let ProcessSendDocumentsHandler = class ProcessSendDocumentsHandler {
    constructor(sendDocuments, emrService, logger, s3Service, statementService) {
        this.sendDocuments = sendDocuments;
        this.emrService = emrService;
        this.logger = logger;
        this.s3Service = s3Service;
        this.statementService = statementService;
    }
    async handle(message) {
        const body = JSON.parse(message.Body || '{}');
        const data = body.data;
        this.logger.log('Processing SQS message', {
            messageId: message.MessageId,
            serviceType: data.serviceType,
            data: JSON.stringify(data)
        });
        switch (data.serviceType) {
            case 'sendIndividualDocuments':
                {
                    // Process documents for email and whatsapp
                    await this.sendDocumentForIndividualAppointment({
                        appointmentId: data === null || data === void 0 ? void 0 : data.appointmentId,
                        shareMode: data === null || data === void 0 ? void 0 : data.shareMode,
                        documentType: data === null || data === void 0 ? void 0 : data.documentType,
                        fileKeys: data === null || data === void 0 ? void 0 : data.fileKeys,
                        type: data === null || data === void 0 ? void 0 : data.type,
                        email: data === null || data === void 0 ? void 0 : data.email,
                        phoneNumber: data === null || data === void 0 ? void 0 : data.phoneNumber
                    });
                }
                break;
            case 'sendReminderNotification':
                {
                    await this.sendDocuments.sendReminderNotification(data === null || data === void 0 ? void 0 : data.reminders);
                }
                break;
            case 'sendPrescriptionDocuments':
                {
                    await this.sendDocuments.sendUpdatedPrescriptionDocument(data === null || data === void 0 ? void 0 : data.appointmentId, data === null || data === void 0 ? void 0 : data.shareingArray, (data === null || data === void 0 ? void 0 : data.type) || 'client', data === null || data === void 0 ? void 0 : data.phoneNumber, data === null || data === void 0 ? void 0 : data.email);
                }
                break;
            case 'createPrescriptionDocuments':
                {
                    await this.sendDocuments.createUpdatedPrescriptionPdf(data === null || data === void 0 ? void 0 : data.appointmentId);
                }
                break;
            case 'sendGlobalDocuments':
                {
                    const patientId = data === null || data === void 0 ? void 0 : data.patientId;
                    const shareMode = JSON.parse((data === null || data === void 0 ? void 0 : data.shareMode) || 'null');
                    const documentType = JSON.parse((data === null || data === void 0 ? void 0 : data.documentType) || '[]');
                    try {
                        await this.sendDocuments.sendMedicalRecords(patientId, shareMode, documentType);
                    }
                    catch (err) {
                        this.logger.error('failed to send global documents', {
                            err,
                            patientId
                        });
                        throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
                    }
                }
                break;
            case 'sendInvoiceTabDocuments':
                {
                    const patientId = data === null || data === void 0 ? void 0 : data.patientId;
                    const shareMode = data === null || data === void 0 ? void 0 : data.shareMode;
                    const fileKeys = Array.isArray(data === null || data === void 0 ? void 0 : data.fileKeys)
                        ? data.fileKeys
                        : [];
                    if (fileKeys.length === 0) {
                        this.logger.error('No file keys provided', {
                            patientId
                        });
                        throw new common_1.HttpException('No file keys provided', common_1.HttpStatus.BAD_REQUEST);
                    }
                    await this.sendDocuments.sendInvoiceTabDocuments(patientId, fileKeys, JSON.parse(shareMode));
                }
                break;
            case 'sendLedgerDocuments':
                {
                    const { invoiceReferenceIds, paymentIdsArray, type, email, phoneNumber, shareMode, shareingArray, filters } = data;
                    const invoiceIds = invoiceReferenceIds || paymentIdsArray || [];
                    const shareModeToUse = shareMode || shareingArray || [];
                    await this.sendDocuments.shareledgerDocuments(invoiceIds, shareModeToUse, type, email, phoneNumber, filters);
                }
                break;
            case 'storeLedgerDocuments':
                {
                    const { invoiceReferenceIds, paymentIdsArray, requestId, filters, userContext } = data;
                    const invoiceIds = invoiceReferenceIds || paymentIdsArray || [];
                    await this.sendDocuments.storeLedgerDocuments(invoiceIds, requestId, filters, userContext);
                }
                break;
            case 'sendVaccinationDocuments':
                {
                    const shareMode = data === null || data === void 0 ? void 0 : data.shareMode;
                    const fileKey = (data === null || data === void 0 ? void 0 : data.fileKey) || '';
                    await this.sendDocuments.sendVaccinationFromFileKey(fileKey, JSON.parse(shareMode), (data === null || data === void 0 ? void 0 : data.type) || 'client', data === null || data === void 0 ? void 0 : data.email, data === null || data === void 0 ? void 0 : data.phoneNumber);
                }
                break;
            case 'diagnosticNotes':
                this.logger.log('Processing diagnostic notes for appointment', data === null || data === void 0 ? void 0 : data.appointmentId);
                await this.sendDocuments.sendDiagnosticTabDocument(data === null || data === void 0 ? void 0 : data.appointmentId, JSON.parse((data === null || data === void 0 ? void 0 : data.shareMode) || 'null'), (data === null || data === void 0 ? void 0 : data.fileKeys) || [], (data === null || data === void 0 ? void 0 : data.type) || 'client', (data === null || data === void 0 ? void 0 : data.email) || '');
                break;
            case 'sendPaymentReceipts':
                {
                    const { paymentReferenceIds, type, email, phoneNumber, shareOptions } = data;
                    const receiptIds = Array.isArray(paymentReferenceIds)
                        ? paymentReferenceIds
                        : [];
                    const shareParams = Array.isArray(shareOptions)
                        ? shareOptions
                        : [];
                    await this.sendDocuments.sharePaymentReceipts(receiptIds, shareParams, type, email, phoneNumber);
                }
                break;
            case 'storePaymentReceipts':
                {
                    const { paymentReferenceIds, requestId } = data;
                    const receiptIds = Array.isArray(paymentReferenceIds)
                        ? paymentReferenceIds
                        : [];
                    await this.sendDocuments.storePaymentReceipts(receiptIds, requestId);
                }
                break;
            case 'generateAndProcessStatement':
                this.logger.log('Processing statement generation task from SQS', {
                    requestId: data.requestId
                });
                try {
                    await this.statementService.processStatementGenerationTask(data);
                }
                catch (error) {
                    this.logger.error(`Error processing generateAndProcessStatement task for requestId: ${data.requestId}`, {
                        error,
                        requestId: data.requestId
                    });
                }
                break;
            case 'processAnalyticsDocuments':
                {
                    this.logger.log('Processing analytics documents task from SQS', {
                        requestId: data.requestId
                    });
                    try {
                        await this.sendDocuments.processAnalyticsDocuments(data.requestId);
                    }
                    catch (error) {
                        // The error is already logged and the request status is set to FAILED
                        // inside processAnalyticsDocuments. We should not re-throw here,
                        // as that would trigger SQS redrive. The message has been handled.
                        this.logger.error(`Business error processing analytics documents task for requestId: ${data.requestId}. The request has been marked as FAILED. Message will not be retried.`, {
                            error,
                            requestId: data.requestId,
                            sqsAction: 'message_completed_without_retry'
                        });
                        // Do NOT re-throw the error to prevent infinite SQS retries
                        // The business logic has already handled the failure state
                    }
                }
                break;
            default:
                this.logger.warn('Unknown serviceType', {
                    serviceType: data.serviceType
                });
                throw new common_1.HttpException('Invalid serviceType', common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async sendDocumentForIndividualAppointment(data) {
        const appointmentId = data === null || data === void 0 ? void 0 : data.appointmentId;
        const shareMode = JSON.parse((data === null || data === void 0 ? void 0 : data.shareMode) || 'null');
        const documentType = JSON.parse((data === null || data === void 0 ? void 0 : data.documentType) || '[]');
        const fileKeys = (data === null || data === void 0 ? void 0 : data.fileKeys) || [];
        const recipientType = data === null || data === void 0 ? void 0 : data.type;
        const recipientEmail = data === null || data === void 0 ? void 0 : data.email;
        const recipientPhone = data === null || data === void 0 ? void 0 : data.phoneNumber;
        try {
            // Check if email is included in share mode
            if (shareMode.includes('email')) {
                this.logger.log('Email delivery requested - will process email documents in one go');
                // First collect all documents if email delivery is requested
                // Create a proxy for the sendMail method to collect attachments
                const buffers = [];
                const fileNames = [];
                // Save original sendMail method
                const originalSendMail = this.sendDocuments.sendMail;
                let clientEmail = recipientEmail || ''; // Use provided email if available
                let emailBody = '';
                let emailSubject = '';
                // Replace sendMail with a collector function
                this.sendDocuments.sendMail = async (body, docBuffers, docFileNames, email, subject) => {
                    // Add validation to ensure we have valid data
                    if (Array.isArray(docBuffers) && docBuffers.length > 0 &&
                        Array.isArray(docFileNames) && docFileNames.length > 0) {
                        buffers.push(...docBuffers);
                        fileNames.push(...docFileNames);
                        // Store the email details from the first call with valid data
                        if (!clientEmail && email) {
                            clientEmail = email;
                            emailBody = body;
                            emailSubject = subject || 'Medical Documents';
                        }
                        else if (!emailBody) {
                            // Still capture the email body and subject even if we already have an email
                            emailBody = body;
                            emailSubject = subject || 'Medical Documents';
                        }
                    }
                    else {
                        this.logger.warn('Invalid document buffers or filenames in sendMail call', {
                            appointmentId,
                            hasDocBuffers: Array.isArray(docBuffers) && docBuffers.length > 0,
                            hasDocFileNames: Array.isArray(docFileNames) && docFileNames.length > 0
                        });
                    }
                    // Don't actually send the email yet
                    return;
                };
                // Process each document type to collect files
                const processPromises = documentType.map((docType) => {
                    // If using a custom recipient, pass that information to methods that support it
                    const customRecipient = recipientType === 'other' && recipientEmail;
                    switch (docType) {
                        case 'emr':
                            // If custom recipient is specified, use direct method
                            if (customRecipient) {
                                return this.emrService.sendEmrOnEmail(appointmentId, 'other', recipientEmail);
                            }
                            return this.emrService.getIndividualEMR(appointmentId, ['email']);
                        case 'invoices':
                            return this.sendDocuments.sendInvoiceDocument(appointmentId, ['email'], recipientType, recipientEmail);
                        case 'diagnostics':
                            return this.sendDocuments.sendDiagnosticDocument(appointmentId, ['email'], recipientType, recipientEmail);
                        case 'diagnosticTab':
                            return this.sendDocuments.sendDiagnosticTabDocument(appointmentId, ['email'], [], recipientType, recipientEmail);
                        case 'diagnosticNotes':
                            return this.sendDocuments.sendDiagnosticTabDocument(appointmentId, ['email'], fileKeys, recipientType, recipientEmail);
                        case 'vaccinations':
                            return this.sendDocuments.sendVaccinationDocument(appointmentId, ['email'], recipientType, recipientEmail);
                        default:
                            return null;
                    }
                });
                await Promise.all(processPromises);
                // Restore original sendMail function
                this.sendDocuments.sendMail = originalSendMail;
                // Now send all documents in one email if we collected any
                if (buffers.length > 0 && clientEmail) {
                    this.logger.log(`Sending ${buffers.length} documents in one consolidated email to ${clientEmail}`);
                    await this.sendDocuments.sendMail(emailBody, buffers, fileNames, clientEmail, emailSubject);
                }
                else {
                    this.logger.warn('No documents collected for email', {
                        appointmentId,
                        hasBuffers: buffers.length > 0,
                        hasClientEmail: !!clientEmail
                    });
                }
            }
            // If WhatsApp is also requested, process it separately
            if (shareMode.includes('whatsapp')) {
                await this.processDocumentsForWhatsApp(documentType, appointmentId, fileKeys, recipientType, recipientPhone);
            }
        }
        catch (err) {
            this.logger.error('Failed to process documents', { err, appointmentId });
            throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    // Helper method to process documents for WhatsApp
    async processDocumentsForWhatsApp(documentType, appointmentId, fileKeys = [], recipientType, recipientPhone) {
        // For whatsapp processing with custom recipient if provided
        const customRecipient = recipientType === 'other' && recipientPhone;
        return Promise.all(documentType.map((docType) => {
            switch (docType) {
                case 'emr':
                    // If there's a custom recipient, use the appropriate logic
                    if (customRecipient) {
                        // The EMR service has methods to handle custom recipients
                        return this.emrService.sendEmrOnWhatsapp(appointmentId, 'other', recipientPhone);
                    }
                    return this.emrService.getIndividualEMR(appointmentId, ['whatsapp']);
                case 'invoices':
                    return this.sendDocuments.sendInvoiceDocument(appointmentId, ['whatsapp'], recipientType, undefined, recipientPhone);
                case 'diagnostics':
                    return this.sendDocuments.sendDiagnosticDocument(appointmentId, ['whatsapp'], recipientType, undefined, recipientPhone);
                case 'diagnosticTab':
                    return this.sendDocuments.sendDiagnosticTabDocument(appointmentId, ['whatsapp'], [], recipientType, undefined, recipientPhone);
                case 'diagnosticNotes':
                    return this.sendDocuments.sendDiagnosticTabDocument(appointmentId, ['whatsapp'], fileKeys, recipientType, undefined, recipientPhone);
                case 'vaccinations':
                    return this.sendDocuments.sendVaccinationDocument(appointmentId, ['whatsapp'], recipientType, undefined, recipientPhone);
                default:
                    return null;
            }
        }));
    }
};
exports.ProcessSendDocumentsHandler = ProcessSendDocumentsHandler;
exports.ProcessSendDocumentsHandler = ProcessSendDocumentsHandler = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => emr_service_1.EmrService))),
    __param(4, (0, common_1.Inject)((0, common_1.forwardRef)(() => statement_service_1.StatementService))),
    __metadata("design:paramtypes", [send_document_service_1.SendDocuments,
        emr_service_1.EmrService,
        winston_logger_service_1.WinstonLogger,
        s3_service_1.S3Service,
        statement_service_1.StatementService])
], ProcessSendDocumentsHandler);
//# sourceMappingURL=process_send_documents.handler.js.map