"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const throttler_1 = require("@nestjs/throttler");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const analytics_service_1 = require("./analytics.service");
const send_document_service_1 = require("../utils/common/send-document.service");
const analytics_dto_1 = require("./dto/analytics.dto");
const share_analytics_documents_dto_1 = require("./dto/share-analytics-documents.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let AnalyticsController = class AnalyticsController {
    constructor(analyticsService, sendDocuments) {
        this.analyticsService = analyticsService;
        this.sendDocuments = sendDocuments;
    }
    async getRevenueChartData(dto) {
        return await this.analyticsService.getRevenueChartData(dto);
    }
    async getCollectedPaymentsChartData(dto) {
        return await this.analyticsService.getCollectedPaymentsChartData(dto);
    }
    async getAppointmentsChartData(dto) {
        return await this.analyticsService.getAppointmentsChartData(dto);
    }
    async downloadReport(dto, res) {
        const buffer = await this.analyticsService.generateReport(dto);
        res.set({
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition': `attachment; filename="${dto.type}-report.xlsx"`,
            'Content-Length': buffer.length,
            'Cache-Control': 'no-cache'
        });
        res.end(buffer);
    }
    async getDoctorSummary(dto) {
        return await this.analyticsService.getDoctorSummary(dto);
    }
    async getSummary(dto) {
        return await this.analyticsService.getSummary(dto);
    }
    async shareAnalyticsDocuments(shareRequest, req) {
        const { clinicId, brandId, ownerId, documentTypes, startDate, endDate, recipientEmail, recipientType } = shareRequest;
        // CRITICAL SECURITY: Validate user can only access their own clinic's data
        if (req.user.clinicId && req.user.clinicId !== clinicId) {
            throw new common_1.HttpException('Access denied: You can only request documents for your own clinic', common_1.HttpStatus.FORBIDDEN);
        }
        if (req.user.brandId && req.user.brandId !== brandId) {
            throw new common_1.HttpException('Access denied: You can only request documents for your own brand', common_1.HttpStatus.FORBIDDEN);
        }
        // BUSINESS RULE: Validate time period is not more than 1 month
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        // Calculate the difference in days
        const timeDifference = endDateObj.getTime() - startDateObj.getTime();
        const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24));
        if (daysDifference > 31) {
            throw new common_1.HttpException('Time period cannot exceed 31 days. Please select a shorter date range.', common_1.HttpStatus.BAD_REQUEST);
        }
        if (startDateObj > endDateObj) {
            throw new common_1.HttpException('Start date cannot be after end date.', common_1.HttpStatus.BAD_REQUEST);
        }
        // Prevent future dates
        const today = new Date();
        today.setHours(23, 59, 59, 999); // End of today
        if (endDateObj > today) {
            throw new common_1.HttpException('End date cannot be in the future.', common_1.HttpStatus.BAD_REQUEST);
        }
        return await this.sendDocuments.shareAnalyticsDocuments(clinicId, brandId, ownerId, req.user.id, documentTypes, new Date(startDate), new Date(endDate), recipientEmail, recipientType);
    }
    async getDocumentShareStatus(requestId, req) {
        return await this.sendDocuments.getAnalyticsDocumentStatus(requestId, req.user);
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Get)('revenue-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get revenue chart data' }),
    (0, track_method_decorator_1.TrackMethod)('get-revenue-chart-data'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetRevenueChartDataDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getRevenueChartData", null);
__decorate([
    (0, common_1.Get)('collected-payments-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get collected payments chart data' }),
    (0, track_method_decorator_1.TrackMethod)('get-collected-payments-chart-data'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetRevenueChartDataDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getCollectedPaymentsChartData", null);
__decorate([
    (0, common_1.Get)('appointments-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get appointments chart data' }),
    (0, track_method_decorator_1.TrackMethod)('get-appointments-chart-data'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetAppointmentsChartDataDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getAppointmentsChartData", null);
__decorate([
    (0, common_1.Get)('download-report'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Download analytics report' }),
    (0, track_method_decorator_1.TrackMethod)('download-analytics-report'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.DownloadAnalyticsReportDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "downloadReport", null);
__decorate([
    (0, common_1.Get)('doctor-summary'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get doctor performance summary' }),
    (0, track_method_decorator_1.TrackMethod)('get-doctor-summary'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetDoctorSummaryDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getDoctorSummary", null);
__decorate([
    (0, common_1.Get)('summary'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get clinic performance summary' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns summary data for clinic'
    }),
    (0, track_method_decorator_1.TrackMethod)('get-clinic-summary'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetSummaryDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getSummary", null);
__decorate([
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    (0, common_1.Post)('share-documents'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({
        summary: 'Share analytics documents via email',
        description: 'This endpoint is rate-limited to 5 requests per minute.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Analytics document sharing request created successfully',
        type: share_analytics_documents_dto_1.ShareAnalyticsDocumentsResponseDto
    }),
    (0, track_method_decorator_1.TrackMethod)('share-analytics-documents'),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ transform: true }))),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [share_analytics_documents_dto_1.ShareAnalyticsDocumentsDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "shareAnalyticsDocuments", null);
__decorate([
    (0, common_1.Get)('share-documents/:requestId/status'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get analytics document sharing status' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns the status of analytics document sharing request',
        type: share_analytics_documents_dto_1.AnalyticsDocumentStatusResponseDto
    }),
    (0, track_method_decorator_1.TrackMethod)('get-analytics-document-status'),
    __param(0, (0, common_1.Param)('requestId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getDocumentShareStatus", null);
exports.AnalyticsController = AnalyticsController = __decorate([
    (0, swagger_1.ApiTags)('Analytics'),
    (0, common_1.Controller)('analytics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService,
        send_document_service_1.SendDocuments])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map