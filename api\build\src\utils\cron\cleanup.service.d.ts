import { Repository } from 'typeorm';
import { <PERSON><PERSON>og<PERSON> } from '../logger/winston-logger.service';
import { S3Service } from '../aws/s3/s3.service';
import { AnalyticsDocumentRequestEntity } from '../../analytics/entities/analytics-document-request.entity';
export declare class FileCleanupService {
    private readonly logger;
    private readonly s3Service;
    private readonly analyticsDocumentRequestRepository;
    constructor(logger: WinstonLogger, s3Service: S3Service, analyticsDocumentRequestRepository: Repository<AnalyticsDocumentRequestEntity>);
    handleExpiredDocuments(): Promise<void>;
}
