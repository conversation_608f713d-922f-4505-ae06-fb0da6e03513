import { Socket, Server } from 'socket.io';
import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Redis, Cluster } from 'ioredis';
import { RedisService } from '../utils/redis/redis.service';
interface CartState {
    isAdjustmentInInvoice: boolean;
    discountObject: {
        label: string;
        value: string;
    };
    invoiceAmount: number;
}
interface CartChangePayload {
    appointmentId: string;
    action: 'add' | 'delete' | 'update' | 'checkout' | 'updateState';
    value: any;
    state?: Partial<CartState>;
}
export declare class CartGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly redisService;
    server: Server;
    private readonly logger;
    protected redisSubClient: Redis | Cluster;
    protected redisPubClient: Redis | Cluster;
    private clientInfoMap;
    private cartStates;
    constructor(redisService: RedisService);
    handleConnection(client: Socket): Promise<void>;
    handleDisconnect(client: Socket): Promise<void>;
    handleJoinCart(client: Socket, { appointmentId }: {
        appointmentId: string;
    }): Promise<void>;
    handleCartChange(client: Socket, payload: CartChangePayload): Promise<void>;
    private handleRedisMessage;
    private publishCartUpdate;
}
export {};
